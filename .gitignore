# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# Virtual environment
venv/
env/

# Environment variable files
.env

# VSCode / IDE configs
.vscode/
.idea/
*.sublime-project
*.sublime-workspace

# MacOS metadata
.DS_Store

# Logs & local outputs
*.log

# Jupyter (if ever used)
.ipynb_checkpoints/

# MyPy / Pyright / Pylint cache
.mypy_cache/
.pyright/
.pytest_cache/

# Coverage / testing
.coverage
htmlcov/
coverage.xml

# Build
build/
dist/
*.egg-info/

# Docker / Compose override
docker-compose.override.yml
scripts/prompt.txt