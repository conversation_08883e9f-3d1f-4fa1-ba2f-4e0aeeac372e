module.exports = {
  apps: [
    {
      name: 'fin-task-aggregator',
      script: 'main.py',
      interpreter: 'python3',
      cwd: '/opt/00_APPS/FINITY_AI/fin_task_aggregator',
      
      // Environment variables
      env: {
        NODE_ENV: 'development',
        PYTHONPATH: '/opt/00_APPS/FINITY_AI/fin_task_aggregator',
        PYTHONUNBUFFERED: '1'
      },
      
      env_production: {
        NODE_ENV: 'production',
        PYTHONPATH: '/opt/00_APPS/FINITY_AI/fin_task_aggregator',
        PYTHONUNBUFFERED: '1'
      },
      
      // Process management
      instances: 1,
      exec_mode: 'fork',
      
      // Auto restart configuration
      autorestart: true,
      watch: false,
      max_memory_restart: '1G',
      
      // Restart policy
      restart_delay: 4000,
      max_restarts: 10,
      min_uptime: '10s',
      
      // Logging
      log_file: './logs/combined.log',
      out_file: './logs/out.log',
      error_file: './logs/error.log',
      log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
      merge_logs: true,
      
      // Health monitoring
      kill_timeout: 5000,
      listen_timeout: 3000,
      
      // Additional options
      ignore_watch: [
        'node_modules',
        'logs',
        '__pycache__',
        '*.pyc',
        '.git',
        'venv'
      ],
      
      // Environment file
      env_file: '.env'
    }
  ],
  
  // Deployment configuration (optional)
  deploy: {
    production: {
      user: 'deploy',
      host: 'your-server.com',
      ref: 'origin/main',
      repo: '**************:your-repo/fin-task-aggregator.git',
      path: '/opt/00_APPS/FINITY_AI/fin_task_aggregator',
      'post-deploy': 'pip install -r requirements.txt && pm2 reload ecosystem.config.js --env production'
    }
  }
};
