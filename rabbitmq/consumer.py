import pika
import json
from config.config import settings
from agent.aggregator import dispatch_first_step
from utils.logger import logger

def consume_task_step_input():
    credentials = pika.PlainCredentials(settings.RABBITMQ_USER, settings.RABBITMQ_PASS)
    connection = pika.BlockingConnection(
        pika.ConnectionParameters(
            host=settings.RABBITMQ_HOST,
            port=settings.RABBITMQ_PORT,
            credentials=credentials
        )
    )
    channel = connection.channel()
    queue = settings.QUEUE_TASK_STEP_INPUT
    channel.queue_declare(queue=queue, durable=True)

    def callback(ch, method, properties, body):
        try:
            data = json.loads(body)
            logger.info(f"📥 Received task for session {data['session_id']}")
            dispatch_first_step(data)
            ch.basic_ack(delivery_tag=method.delivery_tag)
        except Exception as e:
            logger.error(f"❌ Error: {e}")
            ch.basic_nack(delivery_tag=method.delivery_tag, requeue=False)

    channel.basic_qos(prefetch_count=1)
    channel.basic_consume(queue=queue, on_message_callback=callback)

    logger.info(f"👂 Listening on {queue} ...")
    channel.start_consuming()
