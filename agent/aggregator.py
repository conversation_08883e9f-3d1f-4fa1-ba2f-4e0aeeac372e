import redis
import json
from datetime import datetime
from typing import List, Optional
from pydantic import BaseModel
from config.config import settings
from utils.logger import logger
from rabbitmq.publisher import publish_to_queue


class TaskStep(BaseModel):
    step: int
    intent: str
    prompt: str
    complete: bool = False


class TaskData(BaseModel):
    user_id: str
    session_id: str
    timestamp: str
    status: str
    steps: List[TaskStep]

redis_client = redis.Redis(
    host=settings.REDIS_HOST,
    port=settings.REDIS_PORT,
    password=settings.REDIS_PASSWORD,
    db=settings.REDIS_DB,
    decode_responses=True,
)


def save_task_data(payload: dict):
    """Save complete task data to Redis with enhanced structure"""
    session_id = payload["session_id"]
    key = f"task:{session_id}"

    # Create TaskStep objects with complete=False initially
    task_steps = [
        TaskStep(
            step=step["step"],
            intent=step["intent"],
            prompt=step["prompt"],
            complete=False
        ) for step in payload["steps"]
    ]

    # Create TaskData object
    task_data = TaskData(
        user_id=payload["user_id"],
        session_id=payload["session_id"],
        timestamp=payload.get("timestamp", datetime.now().isoformat()),
        status=payload.get("status", "task.split.success"),
        steps=task_steps
    )

    # Save to Redis
    redis_client.set(key, task_data.model_dump_json())
    logger.info(f"✅ Complete task data saved for session {session_id}")


def update_step_completion(session_id: str, step_number: int, complete: bool = True):
    """Update the completion status of a specific step"""
    key = f"task:{session_id}"
    data = redis_client.get(key)
    if data:
        task_data = TaskData.model_validate_json(data)

        # Find and update the specific step
        for step in task_data.steps:
            if step.step == step_number:
                step.complete = complete
                break

        # Save updated data back to Redis
        redis_client.set(key, task_data.model_dump_json())
        logger.info(f"📝 Step {step_number} completion updated: {complete}")
    else:
        logger.error(f"❌ No task data found for session {session_id}")


def update_task_status(session_id: str, status: str):
    """Update the overall task status"""
    key = f"task:{session_id}"
    data = redis_client.get(key)
    if data:
        task_data = TaskData.model_validate_json(data)
        task_data.status = status

        # Save updated data back to Redis
        redis_client.set(key, task_data.model_dump_json())
        logger.info(f"📝 Task status updated: {status}")
    else:
        logger.error(f"❌ No task data found for session {session_id}")


# Legacy function for backward compatibility
def save_metadata(session_id: str, steps: list):
    """Legacy function - use save_task_data instead"""
    logger.warning("⚠️ save_metadata is deprecated, use save_task_data instead")
    key = f"task:{session_id}"
    metadata = {
        "steps_total": len(steps),
        "steps_status": {str(step["step"]): "pending" for step in steps},
    }
    redis_client.set(key, json.dumps(metadata))
    logger.info(f"✅ Metadata saved for session {session_id}")


# Legacy function for backward compatibility
def update_step_status(session_id: str, step: int, status: str):
    """Legacy function - use update_step_completion instead"""
    logger.warning("⚠️ update_step_status is deprecated, use update_step_completion instead")
    key = f"task:{session_id}"
    data = redis_client.get(key)
    if data:
        meta = json.loads(data)
        meta["steps_status"][str(step)] = status
        redis_client.set(key, json.dumps(meta))
        logger.info(f"📝 Step {step} status updated: {status}")


def get_task_data(session_id: str) -> Optional[TaskData]:
    """Retrieve complete task data from Redis"""
    key = f"task:{session_id}"
    data = redis_client.get(key)
    if data:
        try:
            return TaskData.model_validate_json(data)
        except Exception as e:
            logger.error(f"❌ Error parsing task data for session {session_id}: {e}")
            return None
    return None


def get_next_incomplete_step(session_id: str) -> Optional[TaskStep]:
    """Get the next incomplete step for a task"""
    task_data = get_task_data(session_id)
    if task_data:
        for step in task_data.steps:
            if not step.complete:
                return step
    return None


def mark_step_complete_and_get_next(session_id: str, completed_step: int) -> Optional[TaskStep]:
    """Mark a step as complete and return the next incomplete step"""
    update_step_completion(session_id, completed_step, True)
    return get_next_incomplete_step(session_id)


def is_task_complete(session_id: str) -> bool:
    """Check if all steps in a task are complete"""
    task_data = get_task_data(session_id)
    if task_data:
        return all(step.complete for step in task_data.steps)
    return False


def get_task_progress(session_id: str) -> dict:
    """Get task progress summary"""
    task_data = get_task_data(session_id)
    if task_data:
        total_steps = len(task_data.steps)
        completed_steps = sum(1 for step in task_data.steps if step.complete)
        return {
            "session_id": session_id,
            "total_steps": total_steps,
            "completed_steps": completed_steps,
            "progress_percentage": (completed_steps / total_steps * 100) if total_steps > 0 else 0,
            "is_complete": completed_steps == total_steps,
            "status": task_data.status
        }
    return {"error": "Task not found"}


def list_all_tasks() -> List[dict]:
    """List all tasks stored in Redis"""
    tasks = []
    pattern = "task:*"

    for key in redis_client.scan_iter(match=pattern):
        session_id = key.replace("task:", "")
        progress = get_task_progress(session_id)
        if "error" not in progress:
            tasks.append(progress)

    return tasks


def delete_task_data(session_id: str) -> bool:
    """Delete task data from Redis"""
    key = f"task:{session_id}"
    result = redis_client.delete(key)
    if result:
        logger.info(f"🗑️ Task data deleted for session {session_id}")
        return True
    else:
        logger.warning(f"⚠️ No task data found to delete for session {session_id}")
        return False


def get_task_steps_summary(session_id: str) -> List[dict]:
    """Get a summary of all steps with their completion status"""
    task_data = get_task_data(session_id)
    if task_data:
        return [
            {
                "step": step.step,
                "intent": step.intent,
                "prompt": step.prompt[:100] + "..." if len(step.prompt) > 100 else step.prompt,
                "complete": step.complete
            }
            for step in task_data.steps
        ]
    return []


def dispatch_first_step(payload: dict):
    """Enhanced dispatch function using new Redis storage structure"""
    session_id = payload["session_id"]

    # Save complete task data to Redis
    save_task_data(payload)

    # Get the first step and send it
    first_step = get_next_incomplete_step(session_id)
    if first_step:
        send_step(first_step.model_dump(), payload)
        logger.info(f"🚀 Dispatched first step {first_step.step} for session {session_id}")
    else:
        logger.error(f"❌ No steps found for session {session_id}")


def dispatch_next_step(session_id: str, completed_step: int):
    """Dispatch the next step after marking the current one as complete"""
    next_step = mark_step_complete_and_get_next(session_id, completed_step)

    if next_step:
        # Get the original payload data to send with the next step
        task_data = get_task_data(session_id)
        if task_data:
            payload = {
                "user_id": task_data.user_id,
                "session_id": task_data.session_id,
                "timestamp": task_data.timestamp,
                "status": task_data.status,
                "steps": [step.model_dump() for step in task_data.steps]
            }
            send_step(next_step.model_dump(), payload)
            logger.info(f"🚀 Dispatched next step {next_step.step} for session {session_id}")
        else:
            logger.error(f"❌ Could not retrieve task data for session {session_id}")
    else:
        # No more steps, task is complete
        update_task_status(session_id, "task.complete")
        logger.info(f"🎉 All steps completed for session {session_id}")

        # Log final progress
        progress = get_task_progress(session_id)
        logger.info(f"📊 Final progress: {progress}")


def send_step(step: dict, payload: dict):
    intent = step["intent"]
    queue_map = {
        "query": settings.QUEUE_QUERY,
        "summary": settings.QUEUE_SUMMARY,
        "chart": settings.QUEUE_CHART,
    }

    queue = queue_map.get(intent)
    if not queue:
        logger.error(f"🚫 Unknown intent: {intent}")
        return

    message = {
        "user_id": payload["user_id"],
        "session_id": payload["session_id"],
        "step": step["step"],
        "prompt": step["prompt"],
    }

    publish_to_queue(queue, message)
