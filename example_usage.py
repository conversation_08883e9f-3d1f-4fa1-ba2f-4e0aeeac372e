#!/usr/bin/env python3
"""
Example usage of the enhanced Redis storage functionality
This demonstrates how to use the new features in a real scenario
"""

import json
import sys
import os
from datetime import datetime

# Add the project root to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from agent.aggregator import (
    save_task_data,
    get_task_data,
    dispatch_first_step,
    dispatch_next_step,
    get_task_progress,
    get_task_steps_summary,
    is_task_complete
)
from utils.logger import logger


def example_workflow():
    """Example of a complete workflow using the enhanced Redis storage"""
    
    print("🚀 Enhanced Redis Storage - Example Workflow")
    print("=" * 60)
    
    # Example 1: Incoming task from RabbitMQ queue
    incoming_task = {
        "user_id": "user-001",
        "session_id": "sess-example-001",
        "timestamp": "2025-07-26T10:30:00Z",
        "status": "task.split.success",
        "steps": [
            {
                "step": 1,
                "intent": "query",
                "prompt": "Ambil data penggunaan ruang pasien selama 2 bulan terakhir"
            },
            {
                "step": 2,
                "intent": "summary", 
                "prompt": "Ringkas hasil dari step 1"
            },
            {
                "step": 3,
                "intent": "chart",
                "prompt": "Buat grafik dari data yang sudah diringkas"
            }
        ]
    }
    
    print("\n📥 1. Incoming Task Data:")
    print(json.dumps(incoming_task, indent=2))
    
    # Step 1: Process incoming task (this would normally be called by RabbitMQ consumer)
    print("\n🔄 2. Processing Task with dispatch_first_step...")
    dispatch_first_step(incoming_task)
    
    # Step 2: Check what's stored in Redis
    print("\n💾 3. Data Stored in Redis:")
    stored_data = get_task_data("sess-example-001")
    if stored_data:
        print(json.dumps(stored_data.model_dump(), indent=2))
    
    # Step 3: Simulate step completion and progression
    print("\n⏭️ 4. Simulating Step Progression...")
    
    session_id = "sess-example-001"
    
    # Simulate completing step 1
    print("\n   📝 Step 1 completed, dispatching next step...")
    dispatch_next_step(session_id, 1)
    
    # Check progress
    progress = get_task_progress(session_id)
    print(f"   📊 Progress after step 1: {progress['completed_steps']}/{progress['total_steps']} ({progress['progress_percentage']:.1f}%)")
    
    # Simulate completing step 2
    print("\n   📝 Step 2 completed, dispatching next step...")
    dispatch_next_step(session_id, 2)
    
    # Check progress
    progress = get_task_progress(session_id)
    print(f"   📊 Progress after step 2: {progress['completed_steps']}/{progress['total_steps']} ({progress['progress_percentage']:.1f}%)")
    
    # Simulate completing step 3 (final step)
    print("\n   📝 Step 3 completed...")
    dispatch_next_step(session_id, 3)
    
    # Final progress
    progress = get_task_progress(session_id)
    print(f"   📊 Final progress: {progress['completed_steps']}/{progress['total_steps']} ({progress['progress_percentage']:.1f}%)")
    print(f"   ✅ Task complete: {progress['is_complete']}")
    
    # Step 4: Show final state
    print("\n🏁 5. Final Task State:")
    final_data = get_task_data(session_id)
    if final_data:
        print(json.dumps(final_data.model_dump(), indent=2))
    
    # Step 5: Show steps summary
    print("\n📋 6. Steps Summary:")
    steps_summary = get_task_steps_summary(session_id)
    for step in steps_summary:
        status = "✅ Complete" if step['complete'] else "⏳ Pending"
        print(f"   Step {step['step']} ({step['intent']}): {status}")
        print(f"      Prompt: {step['prompt']}")
    
    print("\n🎉 Example workflow completed!")


def monitoring_example():
    """Example of monitoring and management functions"""
    
    print("\n🔍 Monitoring Example")
    print("=" * 30)
    
    # Create a few example tasks for monitoring
    tasks = [
        {
            "user_id": "user-002",
            "session_id": "sess-monitor-001",
            "timestamp": datetime.now().isoformat(),
            "status": "task.split.success",
            "steps": [
                {"step": 1, "intent": "query", "prompt": "Get sales data"},
                {"step": 2, "intent": "summary", "prompt": "Summarize sales"}
            ]
        },
        {
            "user_id": "user-003", 
            "session_id": "sess-monitor-002",
            "timestamp": datetime.now().isoformat(),
            "status": "task.split.success",
            "steps": [
                {"step": 1, "intent": "query", "prompt": "Get inventory data"},
                {"step": 2, "intent": "chart", "prompt": "Create inventory chart"}
            ]
        }
    ]
    
    # Save the tasks
    for task in tasks:
        save_task_data(task)
        print(f"📝 Created task: {task['session_id']}")
    
    # Show all tasks
    from agent.aggregator import list_all_tasks
    all_tasks = list_all_tasks()
    
    print(f"\n📊 Total tasks in system: {len(all_tasks)}")
    for task in all_tasks:
        print(f"   Session: {task['session_id']} - Progress: {task['completed_steps']}/{task['total_steps']} - Status: {task['status']}")


if __name__ == "__main__":
    try:
        # Run the main example
        example_workflow()
        
        # Run monitoring example
        monitoring_example()
        
        print("\n✅ All examples completed successfully!")
        
    except Exception as e:
        logger.error(f"❌ Example failed: {e}")
        print(f"❌ Example failed: {e}")
        sys.exit(1)
