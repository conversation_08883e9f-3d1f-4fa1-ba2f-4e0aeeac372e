import os
from dotenv import load_dotenv

load_dotenv()

class Settings:
    RABBITMQ_HOST = os.getenv("RABBITMQ_HOST")
    RABBITMQ_PORT = int(os.getenv("RABBITMQ_PORT"))
    RABBITMQ_USER = os.getenv("RABBITMQ_USER")
    RABBITMQ_PASS = os.getenv("RABBITMQ_PASSWORD")

    REDIS_HOST = os.getenv("REDISLOGS_HOST")
    REDIS_PORT = int(os.getenv("REDISLOGS_PORT"))
    REDIS_PASSWORD = os.getenv("REDISLOGS_PASSWORD")
    REDIS_DB = int(os.getenv("REDISLOGS_DB"))

    QUEUE_TASK_STEP_INPUT = os.getenv("QUEUE_TASK_STEP_INPUT")
    QUEUE_QUERY = os.getenv("QUEUE_QUERY")
    QUEUE_SUMMARY = os.getenv("QUEUE_SUMMARY")
    QUEUE_CHART = os.getenv("QUEUE_CHART")

settings = Settings()
