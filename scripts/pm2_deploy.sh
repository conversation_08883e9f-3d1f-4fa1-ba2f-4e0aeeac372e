#!/bin/bash

# Define colors for aesthetic output
GREEN='\033[0;32m'
CYAN='\033[0;36m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
GRAY='\033[0;37m'
NC='\033[0m' # No Color

echo -e "${CYAN}═════════════════════════════════════════════"
echo -e "🚀 Starting Deployment with PM2"
echo -e "═════════════════════════════════════════════${NC}"

# Load .env variables
if [ -f ".env" ]; then
    export $(grep -v '^#' .env | xargs)
else
    echo -e "${RED}✖ .env file not found. Deployment aborted.${NC}"
    exit 1
fi

# Validate SERVICE_NAME presence
if [ -z "$SERVICE_NAME" ]; then
    echo -e "${RED}✖ SERVICE_NAME is not set in .env. Aborting.${NC}"
    exit 1
fi

# Step 1: Stop existing PM2 process
echo -e "${YELLOW}➤ Stopping existing PM2 process...${NC}"
pm2 delete ecosystem.config.js 2>/dev/null
echo -e "${GREEN}✔ PM2 process deleted (if existed).${NC}"

# Step 2: Build Go binary using SERVICE_NAME
echo -e "${YELLOW}➤ Building Go binary as '${SERVICE_NAME}'...${NC}"
go build -o ./build/${SERVICE_NAME} ./main.go
if [ $? -eq 0 ]; then
    echo -e "${GREEN}✔ Build successful: ./build/${SERVICE_NAME}${NC}"
else
    echo -e "${RED}✖ Build failed. Deployment aborted.${NC}"
    exit 1
fi

# Step 3: Start application via PM2
echo -e "${YELLOW}➤ Starting application with PM2...${NC}"
pm2 start ecosystem.config.js
echo -e "${GREEN}✔ PM2 started successfully.${NC}"

# Step 4: Save PM2 process list
pm2 save > /dev/null

# Step 5: Stream logs
echo -e "${CYAN}═════════════════════════════════════════════"
echo -e "📜 Tailing logs for service: ${SERVICE_NAME}"
echo -e "═════════════════════════════════════════════${NC}"
pm2 logs ${SERVICE_NAME}
