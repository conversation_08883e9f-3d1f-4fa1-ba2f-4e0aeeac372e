#!/bin/bash

OUTPUT_FILE="./scripts/prompt.txt"
>"$OUTPUT_FILE" # Kosongkan file output

ENV_FILE=".env"

# Fungsi menentukan syntax highlight dari ekstensi file
get_file_lang() {
    case "$1" in
    *.go) echo "go" ;;
    *.sh) echo "bash" ;;
    *.env) echo "env" ;;
    *.mod | *.sum) echo "go" ;;
    *) echo "" ;;
    esac
}

# Fungsi untuk mengambil SERVICE_NAME dari .env
get_service_name() {
    if [ -f "$ENV_FILE" ]; then
        # Ambil SERVICE_NAME tanpa ekspansi variabel
        SERVICE_NAME=$(grep -E '^SERVICE_NAME=' "$ENV_FILE" | cut -d '=' -f2- | tr -d '"')
        echo "$SERVICE_NAME"
    else
        echo "UnknownProject"
    fi
}

# Ambil nama service
PROJECT_NAME=$(get_service_name)

# Tulis nama project ke awal file
echo "# Project: $PROJECT_NAME" >>"$OUTPUT_FILE"
echo "" >>"$OUTPUT_FILE"

# Array daftar file yang ingin dicetak
FILES=(
    ".env"
    "main.py"
    "requirements.txt"
    "agent/aggregator.py"
    "config/config.py"
    "rabbitmq/consumer.py"
    "rabbitmq/publisher.py"
    "utils/logger.py"
)

# Tambahkan struktur folder
echo "## Project structure" >>"$OUTPUT_FILE"
tree -a -L 3 -I '.git|logs' >>"$OUTPUT_FILE"
echo "" >>"$OUTPUT_FILE"

# Loop semua file dalam array
for FILE in "${FILES[@]}"; do
    if [ -f "$FILE" ]; then
        LANG=$(get_file_lang "$FILE")
        echo "## $FILE" >>"$OUTPUT_FILE"
        echo "\`\`\`$LANG" >>"$OUTPUT_FILE"
        cat "$FILE" >>"$OUTPUT_FILE"
        echo "\`\`\`" >>"$OUTPUT_FILE"
        echo "" >>"$OUTPUT_FILE"
    else
        echo "⚠️  File not found: $FILE" >&2
    fi
done

echo "✅ Semua file berhasil disimpan ke $OUTPUT_FILE"
