#!/bin/bash

# Define colors
GREEN='\033[0;32m'
CYAN='\033[0;36m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

echo -e "${CYAN}═════════════════════════════════════════════"
echo -e "🔄 Restarting fin-task-aggregator"
echo -e "═════════════════════════════════════════════${NC}"

# Check if process exists
if pm2 describe fin-task-aggregator > /dev/null 2>&1; then
    echo -e "${YELLOW}➤ Restarting fin-task-aggregator...${NC}"
    pm2 restart fin-task-aggregator
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✔ Service restarted successfully.${NC}"
    else
        echo -e "${RED}✖ Failed to restart service.${NC}"
        exit 1
    fi
else
    echo -e "${YELLOW}➤ Process not found. Starting fresh...${NC}"
    pm2 start ecosystem.config.js
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✔ Service started successfully.${NC}"
    else
        echo -e "${RED}✖ Failed to start service.${NC}"
        exit 1
    fi
fi

# Save PM2 configuration
pm2 save > /dev/null

# Show status
echo -e "\n${YELLOW}➤ Current Status:${NC}"
pm2 status

echo -e "\n${CYAN}🎯 Restart complete.${NC}"
