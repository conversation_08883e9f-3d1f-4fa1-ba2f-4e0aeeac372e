#!/bin/bash

# Define colors
GREEN='\033[0;32m'
CYAN='\033[0;36m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${CYAN}═════════════════════════════════════════════"
echo -e "📊 PM2 Status for fin-task-aggregator"
echo -e "═════════════════════════════════════════════${NC}"

# Show PM2 status
echo -e "${YELLOW}➤ Process Status:${NC}"
pm2 status

echo -e "\n${YELLOW}➤ Process Information:${NC}"
pm2 info fin-task-aggregator 2>/dev/null

echo -e "\n${YELLOW}➤ Memory Usage:${NC}"
pm2 monit --no-interaction 2>/dev/null &
MONIT_PID=$!
sleep 2
kill $MONIT_PID 2>/dev/null

echo -e "\n${YELLOW}➤ Recent Logs (last 20 lines):${NC}"
pm2 logs fin-task-aggregator --lines 20 --nostream 2>/dev/null

echo -e "\n${BLUE}Commands available:${NC}"
echo -e "  ${GREEN}pm2 logs fin-task-aggregator${NC}     - View live logs"
echo -e "  ${GREEN}pm2 restart fin-task-aggregator${NC}  - Restart service"
echo -e "  ${GREEN}pm2 reload fin-task-aggregator${NC}   - Reload service (zero downtime)"
echo -e "  ${GREEN}pm2 stop fin-task-aggregator${NC}     - Stop service"
echo -e "  ${GREEN}pm2 start fin-task-aggregator${NC}    - Start service"
