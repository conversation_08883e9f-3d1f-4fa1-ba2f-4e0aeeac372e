#!/bin/bash

# Define colors
GREEN='\033[0;32m'
CYAN='\033[0;36m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

echo -e "${CYAN}═════════════════════════════════════════════"
echo -e "💥 Stopping Python Task Aggregator"
echo -e "═════════════════════════════════════════════${NC}"

# Step 1: Show current PM2 status
echo -e "${YELLOW}➤ Current PM2 status:${NC}"
pm2 status

# Step 2: Flush logs
echo -e "${YELLOW}➤ Flushing logs for fin-task-aggregator...${NC}"
pm2 flush fin-task-aggregator 2>/dev/null
echo -e "${GREEN}✔ Logs flushed.${NC}"

# Step 3: Delete PM2 process
echo -e "${YELLOW}➤ Stopping and deleting fin-task-aggregator process...${NC}"
pm2 delete fin-task-aggregator 2>/dev/null
echo -e "${GREEN}✔ PM2 process deleted.${NC}"

# Step 4: Save PM2 configuration
echo -e "${YELLOW}➤ Saving PM2 configuration...${NC}"
pm2 save
echo -e "${GREEN}✔ PM2 configuration saved.${NC}"

# Step 5: Show final status
echo -e "${YELLOW}➤ Final PM2 status:${NC}"
pm2 status

echo -e "${CYAN}🎯 fin-task-aggregator stopped successfully.${NC}"
