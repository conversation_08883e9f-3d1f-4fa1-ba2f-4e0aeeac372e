#!/bin/bash

# Define colors
GREEN='\033[0;32m'
CYAN='\033[0;36m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

echo -e "${CYAN}═════════════════════════════════════════════"
echo -e "💥 Destroying PM2 Application"
echo -e "═════════════════════════════════════════════${NC}"

# Load .env variables
if [ -f ".env" ]; then
    export $(grep -v '^#' .env | xargs)
else
    echo -e "${RED}✖ .env file not found. Destruction aborted.${NC}"
    exit 1
fi

# Validate SERVICE_NAME presence
if [ -z "$SERVICE_NAME" ]; then
    echo -e "${RED}✖ SERVICE_NAME is not set in .env. Aborting.${NC}"
    exit 1
fi

# Step 1: Flush logs
echo -e "${YELLOW}➤ Flushing logs for service: ${SERVICE_NAME}${NC}"
pm2 flush ${SERVICE_NAME}
echo -e "${GREEN}✔ Logs flushed.${NC}"

# Step 2: Delete PM2 process
echo -e "${YELLOW}➤ Deleting PM2 process from ecosystem.config.js...${NC}"
pm2 delete ecosystem.config.js 2>/dev/null
echo -e "${GREEN}✔ PM2 process deleted.${NC}"

# Step 3: Reset process ID numbers
echo -e "${YELLOW}➤ Resetting PM2 process ID sequence...${NC}"
pm2 reset all
echo -e "${GREEN}✔ Process ID reset complete.${NC}"

echo -e "${CYAN}🎯 Destruction complete. Environment is clean.${NC}"
