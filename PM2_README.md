# PM2 Ecosystem Configuration for fin-task-aggregator

## Overview
This project includes a complete PM2 ecosystem configuration for running the Python-based task aggregator service. The configuration provides process management, logging, monitoring, and deployment automation.

## Files Structure
```
├── ecosystem.config.js          # Main PM2 configuration
├── scripts/
│   ├── pm2_deploy.sh           # Deploy and start service
│   ├── pm2_remove.sh           # Stop and remove service
│   ├── pm2_restart.sh          # Restart service
│   └── pm2_status.sh           # Check service status
├── logs/                       # Log files directory
└── .env                        # Environment variables
```

## Quick Start

### 1. Prerequisites
- PM2 installed globally: `npm install -g pm2`
- Python 3.x with pip
- Virtual environment (recommended)

### 2. Environment Setup
Create a `.env` file with required variables:
```bash
# RabbitMQ Configuration
RABBITMQ_HOST=localhost
RABBITMQ_PORT=5672
RABBITMQ_USER=guest
RABBITMQ_PASSWORD=guest

# Redis Configuration
REDISLOGS_HOST=localhost
REDISLOGS_PORT=6379
REDISLOGS_PASSWORD=
REDISLOGS_DB=0

# Queue Names
QUEUE_TASK_STEP_INPUT=task_step_input
QUEUE_QUERY=query_queue
QUEUE_SUMMARY=summary_queue
QUEUE_CHART=chart_queue
```

### 3. Deploy Service
```bash
# Make scripts executable
chmod +x scripts/*.sh

# Deploy and start the service
./scripts/pm2_deploy.sh
```

## Available Commands

### Deployment & Management
```bash
# Deploy and start service
./scripts/pm2_deploy.sh

# Check service status
./scripts/pm2_status.sh

# Restart service
./scripts/pm2_restart.sh

# Stop and remove service
./scripts/pm2_remove.sh
```

### Direct PM2 Commands
```bash
# Start service
pm2 start ecosystem.config.js

# View logs
pm2 logs fin-task-aggregator

# Monitor resources
pm2 monit

# Restart service
pm2 restart fin-task-aggregator

# Stop service
pm2 stop fin-task-aggregator

# Delete service
pm2 delete fin-task-aggregator

# Save current PM2 configuration
pm2 save

# Resurrect saved processes
pm2 resurrect
```

## Configuration Details

### Process Configuration
- **Name**: `fin-task-aggregator`
- **Script**: `main.py`
- **Interpreter**: `python3`
- **Instances**: 1 (fork mode)
- **Auto-restart**: Enabled
- **Memory limit**: 1GB

### Logging
- **Combined logs**: `./logs/combined.log`
- **Output logs**: `./logs/out.log`
- **Error logs**: `./logs/error.log`
- **Log rotation**: Automatic
- **Date format**: `YYYY-MM-DD HH:mm:ss Z`

### Health Monitoring
- **Max memory restart**: 1GB
- **Restart delay**: 4 seconds
- **Max restarts**: 10
- **Min uptime**: 10 seconds
- **Kill timeout**: 5 seconds

## Environment Variables
The ecosystem configuration supports different environments:
- **Development**: Default environment
- **Production**: Use `pm2 start ecosystem.config.js --env production`

## Troubleshooting

### Common Issues
1. **Service won't start**
   - Check if `.env` file exists and has correct values
   - Verify Python dependencies are installed
   - Check logs: `pm2 logs fin-task-aggregator`

2. **Memory issues**
   - Monitor with: `pm2 monit`
   - Adjust `max_memory_restart` in ecosystem.config.js

3. **Connection issues**
   - Verify RabbitMQ and Redis services are running
   - Check network connectivity
   - Validate credentials in `.env`

### Log Analysis
```bash
# View real-time logs
pm2 logs fin-task-aggregator

# View last 100 lines
pm2 logs fin-task-aggregator --lines 100

# Flush logs
pm2 flush fin-task-aggregator
```

## Production Deployment
For production deployment, update the `deploy` section in `ecosystem.config.js`:
```javascript
deploy: {
  production: {
    user: 'your-user',
    host: 'your-server.com',
    ref: 'origin/main',
    repo: 'your-git-repo',
    path: '/path/to/deployment',
    'post-deploy': 'pip install -r requirements.txt && pm2 reload ecosystem.config.js --env production'
  }
}
```

Then deploy with:
```bash
pm2 deploy production setup
pm2 deploy production
```

## Auto-start on Boot
To automatically start the service on system boot:
```bash
# Generate startup script
pm2 startup

# Save current process list
pm2 save
```
