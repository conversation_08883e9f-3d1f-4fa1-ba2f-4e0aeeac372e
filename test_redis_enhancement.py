#!/usr/bin/env python3
"""
Test script for the enhanced Redis storage functionality
"""

import json
import sys
import os
from datetime import datetime

# Add the project root to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from agent.aggregator import (
    save_task_data,
    get_task_data,
    update_step_completion,
    update_task_status,
    get_next_incomplete_step,
    mark_step_complete_and_get_next,
    is_task_complete,
    get_task_progress,
    get_task_steps_summary,
    list_all_tasks,
    delete_task_data,
    redis_client
)
from utils.logger import logger


def test_sample_data():
    """Test with the sample data provided in the requirements"""
    
    # Sample input data (from queue task.steps)
    sample_payload = {
        "user_id": "user-001",
        "session_id": "sess-005",
        "timestamp": "2025-07-20T13:00:00Z",
        "status": "task.split.success",
        "steps": [
            {
                "step": 1,
                "intent": "query",
                "prompt": "Ambil data penggunaan ruang pasien selama 2 bulan terakhir"
            },
            {
                "step": 2,
                "intent": "summary",
                "prompt": "Ringkas hasil dari step 1"
            }
        ]
    }
    
    print("🧪 Testing Enhanced Redis Storage")
    print("=" * 50)
    
    # Test 1: Save task data
    print("\n1️⃣ Testing save_task_data...")
    save_task_data(sample_payload)
    
    # Test 2: Retrieve task data
    print("\n2️⃣ Testing get_task_data...")
    task_data = get_task_data("sess-005")
    if task_data:
        print(f"✅ Retrieved task data: {task_data.model_dump_json(indent=2)}")
    else:
        print("❌ Failed to retrieve task data")
        return
    
    # Test 3: Get next incomplete step
    print("\n3️⃣ Testing get_next_incomplete_step...")
    next_step = get_next_incomplete_step("sess-005")
    if next_step:
        print(f"✅ Next incomplete step: {next_step.model_dump()}")
    else:
        print("❌ No incomplete steps found")
    
    # Test 4: Mark step as complete
    print("\n4️⃣ Testing update_step_completion...")
    update_step_completion("sess-005", 1, True)
    
    # Test 5: Get progress
    print("\n5️⃣ Testing get_task_progress...")
    progress = get_task_progress("sess-005")
    print(f"✅ Task progress: {json.dumps(progress, indent=2)}")
    
    # Test 6: Get steps summary
    print("\n6️⃣ Testing get_task_steps_summary...")
    steps_summary = get_task_steps_summary("sess-005")
    print(f"✅ Steps summary: {json.dumps(steps_summary, indent=2)}")
    
    # Test 7: Mark second step complete
    print("\n7️⃣ Testing mark_step_complete_and_get_next...")
    next_step = mark_step_complete_and_get_next("sess-005", 2)
    if next_step:
        print(f"✅ Next step after completing step 2: {next_step.model_dump()}")
    else:
        print("✅ No more steps - task is complete")
    
    # Test 8: Check if task is complete
    print("\n8️⃣ Testing is_task_complete...")
    is_complete = is_task_complete("sess-005")
    print(f"✅ Task complete: {is_complete}")
    
    # Test 9: Update task status
    print("\n9️⃣ Testing update_task_status...")
    update_task_status("sess-005", "task.complete")
    
    # Test 10: Final progress check
    print("\n🔟 Final progress check...")
    final_progress = get_task_progress("sess-005")
    print(f"✅ Final progress: {json.dumps(final_progress, indent=2)}")
    
    # Test 11: List all tasks
    print("\n1️⃣1️⃣ Testing list_all_tasks...")
    all_tasks = list_all_tasks()
    print(f"✅ All tasks: {json.dumps(all_tasks, indent=2)}")
    
    print("\n🎉 All tests completed successfully!")
    
    # Verify the final Redis data matches expected format
    print("\n📋 Final Redis Data Verification:")
    print("=" * 50)
    final_data = get_task_data("sess-005")
    if final_data:
        redis_data = final_data.model_dump()
        print("✅ Final data structure matches expected format:")
        print(json.dumps(redis_data, indent=2))
        
        # Verify it matches the expected structure
        expected_keys = {"user_id", "session_id", "timestamp", "status", "steps"}
        actual_keys = set(redis_data.keys())
        
        if expected_keys == actual_keys:
            print("✅ All required keys present")
        else:
            print(f"❌ Missing keys: {expected_keys - actual_keys}")
            print(f"❌ Extra keys: {actual_keys - expected_keys}")
        
        # Verify steps structure
        for step in redis_data["steps"]:
            step_keys = set(step.keys())
            expected_step_keys = {"step", "intent", "prompt", "complete"}
            if step_keys == expected_step_keys:
                print(f"✅ Step {step['step']} structure is correct")
            else:
                print(f"❌ Step {step['step']} structure is incorrect")
                print(f"   Missing: {expected_step_keys - step_keys}")
                print(f"   Extra: {step_keys - expected_step_keys}")


def cleanup_test_data():
    """Clean up test data"""
    print("\n🧹 Cleaning up test data...")
    delete_task_data("sess-005")
    print("✅ Cleanup completed")


if __name__ == "__main__":
    try:
        # Test Redis connection first
        redis_client.ping()
        print("✅ Redis connection successful")
        
        # Run tests
        test_sample_data()
        
        # Ask user if they want to keep the test data
        keep_data = input("\n❓ Keep test data in Redis? (y/N): ").lower().strip()
        if keep_data != 'y':
            cleanup_test_data()
        
    except Exception as e:
        logger.error(f"❌ Test failed: {e}")
        print(f"❌ Test failed: {e}")
        sys.exit(1)
